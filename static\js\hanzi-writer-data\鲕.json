{"strokes": ["M 297 690 Q 363 780 363 783 Q 362 786 362 788 Q 359 798 335 820 Q 313 836 296 838 Q 280 837 286 817 Q 310 760 143 563 Q 133 554 131 549 Q 128 539 141 541 Q 180 545 279 667 L 297 690 Z", "M 335 523 Q 354 545 391 595 Q 427 646 463 675 Q 479 685 467 697 Q 454 707 420 726 Q 411 729 328 699 Q 325 699 297 690 C 268 681 249 672 279 667 Q 295 661 364 676 Q 371 677 373 675 Q 377 672 373 661 Q 340 576 319 536 Q 313 526 311 517 C 301 489 316 500 335 523 Z", "M 190 485 Q 180 491 150 497 Q 138 500 135 495 Q 128 489 136 474 Q 166 405 181 295 Q 182 262 201 239 Q 217 218 223 233 Q 226 242 227 252 L 227 275 Q 226 288 224 305 Q 205 419 201 455 C 198 481 198 481 190 485 Z", "M 384 286 Q 399 265 413 258 Q 423 251 435 270 Q 450 297 475 437 Q 482 464 501 487 Q 511 497 502 510 Q 487 523 449 545 Q 431 552 377 534 Q 350 531 335 523 L 311 517 Q 284 511 258 503 Q 221 493 190 485 C 161 477 173 444 201 455 Q 200 456 202 456 Q 233 468 282 480 L 312 488 Q 405 515 419 503 Q 432 487 430 472 Q 409 330 392 312 C 380 290 380 290 384 286 Z", "M 335 383 Q 360 390 386 395 Q 405 399 397 410 Q 387 420 366 423 Q 353 424 337 419 L 292 402 Q 258 390 229 379 Q 216 375 233 364 Q 239 361 293 372 L 335 383 Z", "M 330 307 Q 331 349 335 383 L 337 419 Q 337 437 339 445 Q 346 466 312 488 C 287 505 275 509 282 480 Q 288 458 292 402 L 293 372 Q 293 341 294 295 C 295 265 329 277 330 307 Z", "M 227 252 Q 228 252 384 286 C 413 292 419 299 392 312 Q 388 315 380 316 Q 362 317 330 307 L 294 295 Q 257 285 227 275 C 199 266 198 246 227 252 Z", "M 120 122 Q 107 121 105 111 Q 102 98 111 90 Q 133 74 165 53 Q 175 49 187 58 Q 217 80 408 156 Q 433 166 452 180 Q 465 187 466 196 Q 462 202 449 199 Q 173 121 120 122 Z", "M 656 570 Q 731 588 810 605 Q 850 615 855 621 Q 864 628 859 637 Q 852 647 824 654 Q 799 660 684 621 Q 569 599 552 596 Q 518 589 543 574 Q 570 559 620 561 L 656 570 Z", "M 610 409 Q 614 416 622 426 Q 676 516 681 521 Q 694 531 686 541 Q 679 554 656 570 C 632 588 614 590 620 561 Q 629 533 588 427 Q 582 414 580 402 C 574 373 594 384 610 409 Z", "M 523 389 Q 510 402 490 407 Q 486 410 481 404 Q 477 398 480 387 Q 498 350 499 320 Q 500 250 489 173 Q 485 152 492 134 Q 502 116 516 105 Q 529 96 531 106 Q 537 109 540 128 Q 544 144 542 204 Q 541 307 544 346 Q 545 350 545 352 C 547 368 546 370 523 389 Z", "M 728 400 Q 767 407 814 410 Q 833 410 842 400 Q 849 393 845 379 Q 848 177 835 149 Q 832 142 821 143 Q 797 147 770 151 Q 745 152 763 136 Q 821 90 847 57 Q 860 42 872 55 Q 888 73 896 101 Q 906 131 900 291 Q 897 352 914 382 Q 927 401 921 408 Q 914 420 866 448 Q 850 461 835 455 Q 810 445 761 435 Q 661 422 610 409 L 580 402 Q 549 396 523 389 C 494 382 516 343 545 352 Q 552 353 559 356 Q 569 360 587 366 L 609 373 Q 651 389 689 393 L 728 400 Z", "M 587 366 Q 606 306 606 198 Q 603 170 616 148 Q 628 127 634 139 Q 644 158 644 201 Q 643 316 645 340 Q 646 352 643 357 Q 634 364 609 373 C 582 383 582 383 587 366 Z", "M 704 136 Q 708 117 712 110 Q 716 106 722 107 Q 732 113 739 156 Q 743 186 744 215 Q 744 239 749 339 Q 749 345 755 368 Q 756 381 747 388 Q 737 395 728 400 C 702 415 680 422 689 393 L 690 391 Q 702 352 702 212 Q 702 155 704 136 Z"], "medians": [[[296, 825], [315, 804], [322, 783], [299, 735], [226, 634], [170, 572], [139, 548]], [[284, 673], [371, 695], [391, 694], [412, 684], [370, 598], [317, 520]], [[144, 487], [169, 465], [175, 448], [213, 239]], [[198, 479], [210, 475], [391, 524], [426, 524], [444, 517], [463, 495], [462, 485], [433, 347], [416, 297], [419, 273]], [[237, 373], [348, 405], [389, 404]], [[291, 477], [308, 464], [314, 443], [313, 332], [308, 313], [298, 304]], [[229, 258], [239, 267], [367, 297], [384, 307]], [[119, 108], [176, 93], [391, 165], [460, 194]], [[545, 586], [610, 583], [806, 630], [849, 631]], [[627, 559], [651, 531], [617, 446], [587, 407]], [[488, 398], [506, 378], [520, 348], [515, 147], [523, 118]], [[531, 385], [554, 376], [683, 409], [838, 431], [874, 407], [881, 391], [872, 317], [874, 209], [863, 124], [855, 112], [825, 117], [764, 145]], [[594, 362], [610, 353], [620, 338], [626, 146]], [[697, 389], [723, 371], [725, 363], [719, 115]]], "radStrokes": [0, 1, 2, 3, 4, 5, 6, 7]}