{"strokes": ["M 324 690 Q 363 745 387 762 Q 396 771 393 783 Q 390 795 367 816 Q 345 835 329 836 Q 313 836 318 816 Q 330 780 252 672 Q 215 623 164 570 Q 154 563 152 557 Q 148 547 161 549 Q 203 555 311 675 L 324 690 Z", "M 336 507 Q 358 529 404 578 Q 449 630 492 657 Q 511 666 500 680 Q 487 693 453 716 Q 438 723 423 714 Q 416 711 357 696 Q 342 693 324 690 C 294 685 293 685 311 675 Q 329 660 391 669 Q 397 670 398 668 Q 402 664 396 653 Q 350 563 320 521 Q 310 508 308 500 C 296 473 315 486 336 507 Z", "M 201 473 Q 189 482 153 489 Q 143 492 139 487 Q 132 481 141 466 Q 172 396 187 284 Q 190 250 209 227 Q 225 206 230 221 Q 234 231 235 247 L 234 269 Q 233 282 231 295 Q 212 403 206 442 C 201 472 201 473 201 473 Z", "M 398 275 Q 411 256 425 250 Q 435 243 447 263 Q 460 288 484 421 Q 491 449 510 471 Q 520 483 510 494 Q 497 509 458 530 Q 443 536 372 516 Q 366 516 336 507 L 308 500 Q 284 496 201 473 C 172 465 179 430 206 442 Q 212 449 293 466 L 324 475 Q 414 496 427 487 Q 440 472 438 457 Q 417 310 400 302 C 389 286 389 286 398 275 Z", "M 341 368 Q 371 375 398 379 Q 417 383 409 394 Q 399 406 379 408 Q 364 409 344 404 L 302 391 Q 269 381 241 371 Q 228 367 245 355 L 303 360 L 341 368 Z", "M 338 298 Q 339 335 341 368 L 344 404 Q 344 428 346 436 Q 352 454 324 475 C 300 494 283 494 293 466 Q 299 454 302 391 L 303 360 Q 303 332 303 287 C 303 257 337 268 338 298 Z", "M 235 247 Q 239 247 248 248 Q 333 266 398 275 C 428 279 429 294 400 302 Q 396 303 389 305 Q 370 306 338 298 L 303 287 Q 266 278 234 269 C 205 261 205 245 235 247 Z", "M 119 121 Q 106 120 103 109 Q 102 96 111 86 Q 133 70 168 50 Q 178 46 191 55 Q 231 85 277 104 Q 347 132 415 162 Q 440 172 460 187 Q 473 194 474 204 Q 470 210 456 207 Q 180 120 119 121 Z", "M 610 593 Q 695 714 702 723 Q 718 754 733 769 Q 742 781 736 794 Q 732 806 702 827 Q 674 843 656 843 Q 637 840 647 818 Q 665 782 602 661 Q 572 606 529 543 Q 519 533 518 526 Q 515 514 529 518 Q 541 521 589 569 L 610 593 Z", "M 589 569 Q 610 557 630 563 Q 721 588 804 600 Q 814 601 818 595 Q 824 585 824 556 Q 823 450 807 393 Q 800 359 788 346 Q 784 343 738 353 Q 719 365 729 342 Q 754 299 766 267 Q 770 251 786 251 Q 799 252 817 273 Q 866 331 879 522 Q 883 565 899 592 Q 912 611 905 619 Q 892 635 861 648 Q 839 658 816 648 Q 735 612 610 593 C 580 588 561 579 589 569 Z", "M 681 329 Q 697 393 718 414 Q 737 435 717 447 Q 663 477 656 474 Q 650 473 647 470 Q 628 455 562 435 C 533 426 547 402 575 412 Q 584 416 635 427 Q 651 431 655 424 Q 661 420 654 391 Q 647 366 639 334 C 632 305 674 300 681 329 Z", "M 557 276 Q 567 275 577 278 Q 611 290 689 305 Q 698 306 698 315 Q 698 321 681 329 C 661 339 661 339 639 334 Q 633 333 629 331 Q 592 316 560 306 C 531 297 527 278 557 276 Z", "M 986 117 Q 974 145 960 232 Q 960 248 953 254 Q 947 258 941 240 Q 908 149 888 122 Q 872 101 823 89 Q 708 61 616 100 Q 579 118 569 141 Q 548 186 557 276 L 560 306 Q 564 352 574 405 Q 575 409 575 412 C 577 426 577 426 562 435 Q 552 444 535 450 Q 525 454 516 450 Q 510 446 515 430 Q 525 396 523 361 Q 510 175 526 124 Q 530 94 558 69 Q 658 -12 859 24 Q 869 27 882 29 Q 930 42 978 76 Q 997 91 986 117 Z"], "medians": [[[328, 823], [351, 786], [342, 762], [259, 646], [200, 586], [160, 557]], [[317, 677], [408, 688], [439, 675], [439, 670], [373, 570], [315, 503]], [[147, 479], [176, 454], [220, 226]], [[211, 453], [220, 464], [387, 506], [425, 509], [453, 501], [471, 480], [446, 352], [427, 288], [431, 266]], [[244, 363], [364, 391], [401, 388]], [[299, 464], [322, 443], [322, 320], [317, 304], [307, 296]], [[241, 253], [247, 260], [383, 289], [392, 296]], [[119, 105], [175, 90], [468, 201]], [[657, 828], [674, 815], [691, 784], [676, 746], [603, 615], [527, 527]], [[598, 571], [806, 621], [839, 620], [859, 604], [844, 434], [822, 346], [806, 317], [791, 307], [742, 340]], [[576, 420], [596, 433], [659, 449], [678, 439], [688, 427], [670, 364], [665, 352], [647, 340]], [[562, 283], [574, 294], [644, 315], [689, 314]], [[522, 443], [543, 420], [547, 407], [535, 243], [543, 148], [551, 122], [571, 94], [620, 66], [668, 51], [755, 45], [825, 53], [889, 72], [937, 108], [952, 247]]], "radStrokes": [0, 1, 2, 3, 4, 5, 6, 7]}