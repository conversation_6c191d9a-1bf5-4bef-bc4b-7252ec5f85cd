{"strokes": ["M 180 732 Q 205 660 207 538 Q 204 324 185 221 Q 175 176 159 130 Q 140 79 173 31 L 175 29 Q 188 14 198 29 Q 231 63 239 223 Q 245 616 260 697 Q 263 716 246 727 Q 221 748 193 757 Q 183 758 176 750 Q 172 743 180 732 Z", "M 384 728 Q 468 761 470 761 Q 477 768 472 775 Q 465 784 441 786 Q 426 786 353 756 Q 326 747 294 739 Q 266 730 289 720 Q 317 708 357 719 L 384 728 Z", "M 408 614 Q 424 621 446 629 Q 464 636 467 639 Q 471 646 468 652 Q 461 659 438 661 Q 422 662 409 656 L 369 637 Q 360 634 352 630 Q 327 620 297 613 Q 272 604 292 595 Q 329 583 366 599 Q 367 600 370 600 L 408 614 Z", "M 405 527 Q 406 573 408 614 L 409 656 Q 409 671 411 688 Q 415 701 409 709 Q 396 722 384 728 C 358 743 349 748 357 719 Q 366 689 369 637 L 370 600 Q 370 566 371 517 C 372 487 404 497 405 527 Z", "M 371 517 Q 275 492 260 492 Q 251 491 250 484 Q 249 474 255 469 Q 271 457 294 444 Q 301 443 309 448 Q 327 463 447 518 Q 463 525 475 534 Q 484 538 484 546 Q 480 550 472 548 Q 439 538 405 527 L 371 517 Z", "M 651 754 Q 684 764 719 773 Q 738 779 741 781 Q 748 788 743 795 Q 736 804 713 808 Q 695 811 623 784 Q 596 777 564 771 Q 536 764 557 752 Q 581 740 619 745 L 651 754 Z", "M 670 646 Q 692 653 718 660 Q 737 666 741 668 Q 745 675 743 680 Q 737 687 716 691 Q 695 694 675 684 Q 674 684 673 683 L 629 667 Q 602 660 571 655 Q 546 648 565 638 Q 595 628 629 634 L 670 646 Z", "M 666 569 Q 667 611 670 646 L 673 683 Q 673 705 675 713 Q 681 731 651 754 C 628 773 610 774 619 745 Q 625 730 629 667 L 629 634 Q 629 604 630 561 C 631 531 665 539 666 569 Z", "M 630 561 Q 545 551 535 548 Q 514 547 530 534 Q 554 515 574 520 Q 664 542 768 553 Q 775 552 782 561 Q 782 570 766 577 Q 736 595 670 570 Q 667 570 666 569 L 630 561 Z", "M 809 731 Q 815 691 828 82 Q 828 57 815 46 Q 814 46 747 50 Q 713 63 715 52 Q 716 45 738 29 Q 799 -23 816 -53 Q 834 -87 849 -88 Q 865 -89 880 -51 Q 899 -2 895 78 Q 870 346 870 617 Q 871 713 884 762 Q 900 796 840 817 Q 803 836 783 828 Q 764 821 782 796 Q 806 768 809 731 Z", "M 590 374 Q 606 378 625 381 Q 671 391 679 397 Q 686 404 682 411 Q 676 420 653 426 Q 629 432 602 420 L 550 405 Q 532 401 453 382 L 410 375 Q 383 374 355 370 Q 325 366 347 351 Q 374 335 416 339 L 456 348 Q 499 358 546 365 L 590 374 Z", "M 453 382 Q 447 437 446 438 Q 425 454 407 460 Q 397 461 390 457 Q 384 453 388 445 Q 400 429 410 375 L 416 339 Q 420 311 427 269 C 432 239 466 244 463 274 Q 459 314 456 348 L 453 382 Z", "M 571 293 Q 581 336 590 374 L 602 420 Q 605 432 615 451 Q 619 458 605 472 Q 581 490 564 496 Q 552 500 546 494 Q 539 490 544 478 Q 553 462 550 405 L 546 365 Q 542 328 536 287 C 532 257 564 264 571 293 Z", "M 466 247 Q 505 262 694 267 Q 742 267 747 274 Q 751 284 738 293 Q 695 324 641 308 Q 610 302 571 293 L 536 287 Q 517 286 501 281 Q 482 280 463 274 L 427 269 Q 363 260 293 249 Q 277 248 289 234 Q 299 224 313 221 Q 329 217 341 221 Q 393 237 460 246 Q 461 247 466 247 Z", "M 410 203 Q 404 152 284 47 Q 266 34 289 37 Q 352 44 437 139 Q 459 164 471 175 Q 478 185 477 193 Q 474 203 452 210 Q 428 217 418 215 Q 412 214 410 203 Z", "M 566 190 Q 597 150 635 92 Q 645 74 655 69 Q 662 68 669 74 Q 679 81 676 117 Q 672 162 571 213 Q 565 219 562 209 Q 561 199 566 190 Z"], "medians": [[[186, 745], [204, 730], [225, 699], [228, 590], [217, 262], [205, 169], [189, 102], [187, 34]], [[289, 730], [346, 734], [440, 769], [466, 769]], [[293, 604], [328, 605], [431, 643], [458, 646]], [[366, 716], [381, 706], [388, 685], [389, 550], [375, 525]], [[262, 480], [304, 475], [477, 542]], [[559, 762], [619, 764], [703, 789], [733, 789]], [[566, 647], [608, 646], [698, 674], [734, 675]], [[625, 743], [646, 729], [650, 719], [648, 584], [634, 569]], [[540, 538], [606, 542], [715, 565], [774, 562]], [[786, 816], [808, 804], [843, 770], [845, 442], [860, 52], [840, 1], [721, 52]], [[349, 362], [381, 356], [419, 358], [635, 406], [671, 406]], [[397, 450], [423, 424], [443, 289], [432, 278]], [[553, 485], [581, 452], [559, 318], [541, 295]], [[301, 239], [332, 237], [520, 272], [666, 290], [741, 279]], [[464, 190], [438, 184], [376, 106], [332, 68], [293, 45]], [[569, 204], [638, 136], [655, 106], [658, 83]]], "radStrokes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}