{"strokes": ["M 220 493 Q 226 641 236 684 Q 239 703 221 715 Q 197 736 169 745 Q 159 746 152 738 Q 148 732 156 720 Q 204 584 171 302 Q 164 208 131 118 Q 122 90 126 69 Q 136 32 147 18 Q 159 2 170 17 Q 204 53 212 211 Q 213 373 219 471 L 220 493 Z", "M 352 705 Q 386 718 427 731 Q 446 738 450 741 Q 456 748 452 755 Q 445 764 421 766 Q 394 767 330 736 Q 300 727 267 719 Q 239 710 261 700 Q 280 693 320 695 L 352 705 Z", "M 371 603 Q 393 612 419 619 Q 438 626 441 628 Q 447 635 443 640 Q 437 647 416 651 Q 395 652 375 642 Q 374 642 373 641 L 328 623 Q 301 616 274 611 Q 249 604 269 594 Q 296 585 329 591 L 371 603 Z", "M 366 525 Q 367 568 371 603 L 373 641 Q 373 656 375 665 Q 381 680 373 688 Q 363 698 352 705 C 327 722 311 724 320 695 Q 324 691 328 623 L 329 591 Q 329 560 330 514 C 331 484 365 495 366 525 Z", "M 330 514 Q 249 495 227 494 Q 223 494 220 493 C 191 489 194 488 219 471 Q 246 453 258 447 Q 271 443 287 452 Q 312 468 436 530 Q 449 536 453 541 Q 459 548 450 550 Q 438 550 366 525 L 330 514 Z", "M 643 729 Q 674 739 710 748 Q 732 755 737 759 Q 743 766 739 771 Q 733 780 711 784 Q 689 787 666 776 Q 641 766 612 756 Q 581 746 544 741 Q 517 734 537 723 Q 567 711 610 720 L 643 729 Z", "M 664 636 Q 739 660 740 660 Q 746 667 743 672 Q 737 679 717 684 Q 695 687 667 673 L 619 656 Q 586 649 551 642 Q 526 636 545 626 Q 575 611 620 625 L 664 636 Z", "M 660 568 Q 661 605 664 636 L 667 673 Q 667 712 655 720 Q 648 726 643 729 C 618 746 597 747 610 720 Q 616 714 619 656 L 620 625 Q 620 598 622 558 C 623 528 659 538 660 568 Z", "M 622 558 Q 598 554 579 550 Q 557 546 530 542 Q 508 538 526 526 Q 550 507 573 514 Q 643 532 756 551 Q 763 550 770 560 Q 771 569 753 576 Q 729 591 660 568 L 622 558 Z", "M 786 722 Q 792 685 800 103 Q 800 78 787 69 Q 781 66 714 82 Q 680 95 683 84 Q 684 77 704 61 Q 768 6 784 -24 Q 803 -60 819 -62 Q 834 -63 849 -26 Q 868 22 865 99 Q 843 355 844 613 Q 845 704 858 752 Q 874 783 816 805 Q 780 824 761 816 Q 742 809 760 785 Q 782 757 786 722 Z", "M 526 408 Q 530 420 526 441 Q 520 463 458 484 Q 445 488 440 487 Q 436 484 436 473 Q 437 464 458 444 Q 473 428 488 407 L 494 401 C 515 380 518 379 526 408 Z", "M 478 368 Q 547 381 658 386 Q 701 386 708 395 Q 711 404 698 413 Q 649 444 600 425 Q 567 419 526 408 L 494 401 Q 493 401 292 365 Q 277 364 289 351 Q 299 341 314 337 Q 330 334 342 338 Q 385 353 436 361 L 478 368 Z", "M 355 275 Q 333 284 319 283 Q 309 279 314 270 Q 339 230 322 156 Q 310 123 339 93 Q 345 83 353 88 Q 374 109 371 157 Q 370 224 370 244 C 370 269 370 269 355 275 Z", "M 503 273 Q 582 289 593 279 Q 603 272 603 244 Q 602 216 603 183 Q 604 158 595 148 Q 591 141 572 145 Q 563 145 553 146 Q 540 150 535 145 Q 531 141 544 132 Q 578 98 598 71 Q 608 61 623 65 Q 630 69 639 89 Q 655 120 654 160 Q 650 248 659 277 Q 666 290 659 298 Q 641 310 611 322 Q 595 329 582 322 Q 554 309 504 298 L 454 287 Q 411 281 355 275 C 325 272 341 237 370 244 L 455 263 L 503 273 Z", "M 455 263 Q 458 -22 470 -40 L 472 -42 Q 476 -48 482 -45 Q 488 -42 493 -30 Q 502 0 502 193 Q 502 227 503 273 L 504 298 Q 504 305 506 309 Q 507 322 510 333 Q 513 348 501 355 Q 488 365 478 368 C 451 381 422 387 436 361 Q 436 360 445 345 Q 454 330 454 287 L 455 263 Z"], "medians": [[[162, 733], [180, 718], [201, 687], [204, 506], [189, 239], [160, 78], [159, 22]], [[262, 710], [310, 711], [409, 746], [442, 749]], [[269, 603], [319, 604], [399, 632], [433, 635]], [[326, 695], [350, 674], [349, 551], [344, 532], [334, 523]], [[224, 487], [236, 478], [270, 474], [448, 544]], [[539, 733], [583, 732], [692, 764], [728, 767]], [[546, 635], [593, 634], [673, 654], [702, 667], [736, 667]], [[616, 720], [640, 701], [643, 681], [641, 590], [627, 566]], [[526, 534], [557, 529], [712, 563], [761, 561]], [[768, 800], [785, 792], [819, 760], [819, 441], [833, 89], [827, 56], [811, 25], [690, 82]], [[443, 479], [488, 446], [519, 414]], [[301, 356], [323, 353], [409, 373], [625, 408], [701, 399]], [[324, 274], [340, 260], [348, 240], [349, 99]], [[363, 273], [372, 264], [388, 263], [559, 298], [594, 303], [609, 298], [628, 280], [629, 268], [629, 170], [620, 129], [603, 115], [541, 140]], [[443, 362], [480, 334], [477, -35]]], "radStrokes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}