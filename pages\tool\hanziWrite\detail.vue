<template>
	<view class="page-con">

		<!-- 顶部文字导航 -->
		<scroll-view scroll-x class="word-nav" :show-scrollbar="false">
			<view class="nav-items">
				<view 
					v-for="(item, index) in navItems" 
					:key="index"
					:class="['nav-item', { active: currentIndex === index }]"
					@click="selectWord(index)"
				>
					{{ item }}
				</view>
				<view class="nav-item icon">
					<u-icon name="grid" size="18"></u-icon>
				</view>
			</view>
		</scroll-view>
  <view>
    
  </view>
		<!-- 汉字信息 -->
		<view class="hanzi-info">
			<text class="pinyin">{{hanziInfo.spell}}</text>
			<view class="character-box">
				<view :prop="currentWord" :change:prop="characterWriter.changeWriter" class="svg-con">
					<!-- 使用 div 作为 HanziWriter 的容器 -->
					<view  class="hanzi-container">
						<!-- 背景网格 -->
						<canvas canvas-id="gridCanvas" class="grid-canvas"></canvas>
						<!-- HanziWriter 会在这个容器中渲染汉字 -->
						 <view   id="grid-background-target">
						 </view>
					</view>
				</view>
			</view>
			
			<!-- 笔画信息 -->
			<view class="stroke-info">
				<text>部首：{{hanziInfo.radical}}</text>
				<text>笔画：{{hanziInfo.stroke}}</text>
				<text>结构：{{hanziInfo.struct}}</text>
			</view>

			<!-- 学习按钮 -->
			<view class="learn-buttons">
				<button class="learn-btn">
					<u-icon name="edit-pen" size="24"></u-icon>
					<text>学一学</text>
				</button>
				<button @click="goWrite" class="learn-btn">
					<u-icon name="edit-pen-fill" color="#ec8403" size="24"></u-icon>
					<text>写一写</text>
				</button>
			</view>
		</view>

		<!-- 释义等内容 -->
		<view class="content-tabs">
			<u-tabs 
				ref="tabsRef"
				:list="[{name: '释义'}, {name: '组词'}, {name: '教材例句'}]" 
				:current="currentTab"
				lineWidth="40"
				lineHeight="4"
				:activeStyle="{
					color: '#333',
					fontWeight: 'bold',
					transform: 'scale(1.05)',
					transition: 'all 0.3s ease'
				}"
				:inactiveStyle="{
					color: '#666',
					transform: 'scale(1)',
					transition: 'all 0.3s ease'
				}"
				itemStyle="padding-left: 30rpx; padding-right: 30rpx; height: 80rpx;"
				@change="handleTabChange">
			</u-tabs>
			
			<view class="tab-content">
				<!-- 释义 -->
				<view v-if="currentTab === 0" class="meaning-content">
					<view class="word-list">
						<!-- <text class="number">1.</text> -->
						<view class="word-item" v-html="wordDetail.shiYi"></view>
					</view>
				<!-- 	<view class="examples">
						<text class="example">地毯、壁毯、毛毯。</text>
					</view> -->
				</view>

				<!-- 组词 -->
				<view v-if="currentTab === 1" class="words-content">
					<view class="word-list">
						<view v-html="wordDetail.zuCi" class="word-item"></view>
					</view>
				</view>

				<!-- 教材例句 -->
				<view v-if="currentTab === 2" class="examples-content">
				<view class="word-list">
					<view v-html="wordDetail.liJu" class="word-item"></view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import http from '/api/index.js';
	import { useHanziStore } from '@/stores/hanzi'
	import { storeToRefs } from 'pinia'
	
	export default {
		data() {
			return {
				wordDetail: {},
				currentTab: 0,
				tabsRef: null,
				currentWord: ''
			}
		},
		computed: {
			navItems() {
				const hanziStore = useHanziStore();
				const { currentWords } = storeToRefs(hanziStore);
				return currentWords.value;
			},
			currentIndex() {
				const hanziStore = useHanziStore();
				const { currentIndex } = storeToRefs(hanziStore);
				return currentIndex.value;
			}
		},
		methods: {
			draw() {
				const ctx = uni.createCanvasContext('gridCanvas', this);
				ctx.setStrokeStyle('#DDD');
				ctx.beginPath();
				ctx.moveTo(0, 0);
				ctx.lineTo(150, 150);
				ctx.moveTo(150, 0);
				ctx.lineTo(0, 150);
				ctx.moveTo(75, 0);
				ctx.lineTo(75, 150);
				ctx.moveTo(0, 75);
				ctx.lineTo(150, 75);
				ctx.stroke();
				ctx.draw();
			},
			
			updateCurrentWord() {
				const hanziStore = useHanziStore();
				const { currentWords, currentIndex } = storeToRefs(hanziStore);
				this.currentWord = currentWords.value[currentIndex.value];
			},
			
			handleTabChange(e) {
				this.currentTab = e.index;
			},
			
			goBack() {
				uni.navigateBack();
			},
			
			selectWord(index) {
				const hanziStore = useHanziStore();
				hanziStore.setCurrentIndex(index);
				this.updateCurrentWord();
				this.getHanziInfo();
			},
			
			init() {
				this.draw();
				const hanziStore = useHanziStore();
				const { currentWords } = storeToRefs(hanziStore);
				
				if (currentWords.value.length === 0) {
					uni.navigateBack();
					return;
				}
				
				this.updateCurrentWord();
				this.getHanziInfo();
			},
			updateHanziInfo(info) {
				this.hanziInfo = info;
			},
			
			getHanziInfo() {
			
				
				http.getCnWordDetail({ cnWord: this.currentWord }).then(res => {
					this.wordDetail = res.data;
				});
			},
			
			goWrite() {
				// 先演示一遍，再进入用户书写模式
				if (this.$refs.characterWriter) {
					this.$refs.characterWriter.demoThenQuiz(this.currentWord);
				}
			}
		},
		mounted() {
			this.init();
			this.$nextTick(() => {
				if (this.$refs.tabsRef) {
					this.$refs.tabsRef.init();
				}
			});
		}
	}
</script>

<script module="characterWriter" lang="renderjs">
	import HanziWriter from 'hanzi-writer';
	import cnchar from 'cnchar';
	import 'cnchar-radical';

	export default {
		data() {
			return {
				writer: null,
				hanziInfo: {
					spell: '',
					stroke: '',
					radical: '',
					struct: ''
				}
			}
		},
		methods: {
			initWriter(character) {
				const container = document.getElementById('grid-background-target');
				if (container) {
					container.innerHTML = '';
					if (this.writer) {
						this.writer = null;
					}
					this.writer = HanziWriter.create(container, character, {
						width: 150,
						height: 150,
						showCharacter: true,
						padding: 5,
						strokeWidth: 2,
						outlineWidth: 0.5,
						strokeColor: '#333',
						outlineColor: '#333',
						strokeAnimationSpeed: 1,
						delayBetweenStrokes: 500,
						charDataLoader: function(char) {
							return fetch(`http://tbookmaster.com/hanzi/${char}.json`).then(res => res.json());
						}
					});
				}
				// 同步hanziInfo到主线程
				this.getCharacterInfo(character)
			},
			
			changeWriter(newVal, oldVal) {
				if (newVal && newVal !== oldVal) {
					this.initWriter(newVal);
				}
			},
			
			getCharacterInfo(character) {
				try {
					const radicalInfo = cnchar.radical(character)[0];
					const hanziInfo = {
						spell: cnchar.spell(character),
						stroke: cnchar.stroke(character),
						radical: radicalInfo.radical,
						struct: radicalInfo.struct
					};
					this.hanziInfo = hanziInfo;
				} catch (error) {
					const empty = { spell: '', stroke: '', radical: '', struct: '' };
					this.hanziInfo = empty;
				}
			},

			demoThenQuiz(character) {
				const container = document.getElementById('grid-background-target');
				if (container) {
					container.innerHTML = '';
					if (this.writer) this.writer = null;
					this.writer = HanziWriter.create(container, character, {
						width: 150,
						height: 150,
						showCharacter: false, // 先不显示静态字
						padding: 5,
						strokeWidth: 2,
						outlineWidth: 0.5,
						strokeColor: '#333',
						outlineColor: '#333',
						strokeAnimationSpeed: 1,
						delayBetweenStrokes: 500,
						charDataLoader: function(char) {
							return fetch(`http://tbookmaster.com/hanzi/${char}.json`).then(res => res.json());
						}
					});
					this.writer.animateCharacter({
						onComplete: () => {
							this.writer.quiz();
						}
					});
				}
			}
		},
		mounted() {
			if (this.prop) {
				this.initWriter(this.prop);
			}
		}
	}
</script>



<style scoped lang="scss">
	.page-con {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.header {
		padding: 20rpx;
		display: flex;
		align-items: center;
		background-color: #fff;
		
		.back-icon {
			padding: 0 20rpx;
			color: #333;
		}
		
		.title {
			flex: 1;
			text-align: center;
			font-size: 30rpx;
			color: #333;
		}
	}

	.hanzi-info {
		background-color: #fff;
		padding: 30rpx;
		margin-bottom: 20rpx;
		
		.pinyin {
			text-align: center;
			display: block;
			font-size: 36rpx;
			margin-bottom: 20rpx;
		}
		
		.character-box {
			width: 300rpx;
			height: 300rpx;
			margin: 40rpx auto 30rpx;
			
			.svg-con {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				
				svg {
					width: 300rpx;
					height: 300rpx;
				}
			}
		}
		
		.stroke-info {
			display: flex;
			justify-content: space-around;
			margin: 20rpx 0;
			color: #666;
			font-size: 26rpx;
			background: #fff;
			position: relative;
			z-index: 1;
			
			text {
				color: #666;
				font-size: 26rpx;
				position: relative;
				padding: 0 20rpx;
				
				&:not(:last-child)::after {
					content: '';
					position: absolute;
					right: 0;
					top: 50%;
					transform: translateY(-50%);
					width: 2rpx;
					height: 24rpx;
					background-color: #ddd;
				}
			}
		}
		
		.learn-buttons {
			display: flex;
			justify-content: center;
			gap: 40rpx;
			margin-top: 30rpx;
			
			.learn-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 10rpx;
				background-color: #FFF0E5;
				border: none;
				border-radius: 8rpx;
				padding: 16rpx 40rpx;
				min-width: 180rpx;
				line-height: 1;
				
				&::after {
					border: none;
				}
				
				&:active {
					opacity: 0.7;
				}
				
				text {
					color: #FF9052;
					font-size: 28rpx;
				}
				
				.u-icon {
					color: #FF9052;
				}
			}
		}
	}

	.content-tabs {
		background-color: #fff;
		
		:deep(.u-tabs) {
			.u-tabs__wrapper__nav__item {
				position: relative;
				transition: all 0.3s ease;
				
				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 0;
					height: 4rpx;
					background-color: #FF9052;
					transition: all 0.3s ease;
				}
				
				&--active {
					color: #333;
					font-weight: bold;
					
					&::after {
						width: 40rpx;
					}
				}
			}
			
			.u-tabs__wrapper__nav {
				position: relative;
				
				&::after {
					content: '';
					position: absolute;
					left: 0;
					right: 0;
					bottom: 0;
					height: 1rpx;
					background-color: #eee;
				}
			}
			
			.u-tabs__wrapper__nav__line {
				background-color: #FF9052;
				transition: all 0.3s ease;
			}
		}
		
		.tab-content {
			padding: 30rpx;
			
			.meaning-content, .examples-content {
				.meaning-item {
					display: flex;
					align-items: flex-start;
					
					.number {
						margin-right: 10rpx;
						color: #333;
					}
				}
				
				.example {
					color: #666;
					margin-top: 10rpx;
					padding-left: 30rpx;
					display: block;
				}
			}
			
			.word-list {
				display: flex;
				flex-wrap: wrap;
				gap: 20rpx;
				padding: 10rpx 0;
				
				.word-item {
					background-color: #f8f8f8;
					padding: 10rpx 30rpx;
					border-radius: 4rpx;
					color: #666;
					word-wrap: break-word;
					word-break: break-all;
					white-space: normal;
					line-height: 1.5;
					max-width: 100%;
					box-sizing: border-box;
				}
			}
			
			.example-item {
				margin-bottom: 20rpx;
				display: flex;
				align-items: flex-start;
				
				.number {
					margin-right: 10rpx;
					color: #333;
					flex-shrink: 0;
				}
				
				text:last-child {
					color: #333;
					line-height: 1.6;
					word-wrap: break-word;
					word-break: break-all;
					white-space: normal;
					max-width: 100%;
					box-sizing: border-box;
				}
			}
			
			.meaning-item, .example-item, .word-item {
				&:active {
					opacity: 0.7;
				}
			}
		}
	}

	.word-nav {
		background-color: #fff;
		white-space: nowrap;
		padding: 20rpx 0;
		
		.nav-items {
			display: inline-flex;
			padding: 0 20rpx;
			align-items: center;
		}
		
		.nav-item {
			display: inline-block;
			padding: 10rpx 30rpx;
			font-size: 28rpx;
			color: #666;
			position: relative;
			border-radius: 4rpx;
			transition: all 0.3s ease;
			
			&.active {
				color: #333;
				font-weight: bold;
				background-color: #FFF0E5;
			}
			
			&:not(:last-child)::after {
				content: '';
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 2rpx;
				height: 24rpx;
				background-color: #eee;
			}
			
			&.icon {
				padding: 10rpx 20rpx;
				display: flex;
				align-items: center;
			}
		}
	}

	.hanzi-container {
		position: relative;
		width: 150px;
		height: 150px;
	}
	.grid-canvas {
		position: absolute;
		left: 0;
		top: 0;
		width: 150px;
		height: 150px;
		z-index: 0;
		pointer-events: none;
	}
	#grid-background-target {
		position: absolute;
		left: 0;
		top: 0;
		width: 150px !important;
		height: 150px !important;
		z-index: 999;
		border: 1px solid #ddd;
		
	}
</style>