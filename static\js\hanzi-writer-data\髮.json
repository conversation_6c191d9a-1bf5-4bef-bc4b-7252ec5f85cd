{"strokes": ["M 331 766 Q 361 762 470 787 Q 477 790 488 792 Q 509 798 512 801 Q 519 808 515 815 Q 508 824 485 828 Q 464 831 390 804 Q 360 797 325 791 Q 322 791 323 790 C 294 781 301 768 331 766 Z", "M 340 551 Q 337 588 335 619 L 334 637 Q 331 674 330 696 L 330 713 Q 330 740 331 766 C 332 783 332 783 323 790 L 321 792 Q 300 807 276 814 Q 264 818 256 813 Q 250 807 256 794 Q 275 760 284 724 Q 291 687 306 544 C 309 514 342 521 340 551 Z", "M 330 696 Q 357 684 449 703 Q 462 706 477 709 Q 496 713 499 716 Q 506 722 502 728 Q 498 735 477 741 Q 455 745 435 736 Q 384 721 333 714 Q 330 714 330 713 C 302 706 302 706 330 696 Z", "M 335 619 Q 363 613 430 623 Q 452 629 478 633 Q 497 637 499 640 Q 506 646 502 652 Q 498 659 477 665 Q 455 669 435 660 Q 389 645 337 638 Q 334 638 334 637 C 305 630 305 624 335 619 Z", "M 324 513 Q 333 523 517 542 Q 532 543 533 551 Q 534 563 518 571 Q 494 581 474 578 Q 395 562 340 551 L 306 544 Q 161 523 105 519 Q 93 518 91 511 Q 87 501 101 491 Q 131 467 182 478 Q 243 497 294 507 L 324 513 Z", "M 451 453 Q 381 434 311 411 Q 305 410 304 410 Q 303 411 306 415 Q 340 470 356 484 Q 366 497 358 503 Q 349 510 324 513 C 294 518 293 518 294 507 Q 293 503 296 493 Q 308 444 240 392 Q 206 376 228 342 Q 238 327 249 324 Q 255 323 263 328 Q 308 373 463 438 C 491 450 480 461 451 453 Z", "M 463 438 Q 475 423 490 413 Q 500 409 508 417 Q 514 426 514 438 Q 513 456 501 467 Q 498 470 449 499 Q 440 505 434 502 Q 430 501 429 492 Q 429 488 451 453 L 463 438 Z", "M 694 821 Q 697 805 644 750 Q 616 723 558 684 Q 551 677 564 675 Q 652 699 748 775 Q 758 785 774 792 Q 784 799 782 808 Q 778 815 761 822 Q 724 835 702 833 Q 693 832 694 821 Z", "M 703 670 Q 673 628 575 561 Q 572 554 577 552 Q 580 553 588 557 Q 664 582 725 630 Q 756 649 785 661 Q 798 664 800 671 Q 801 680 791 686 Q 739 710 714 703 Q 708 688 703 670 Z", "M 735 575 Q 723 524 563 439 Q 554 432 568 430 Q 662 443 784 520 Q 797 530 814 536 Q 827 543 824 551 Q 821 558 804 569 Q 767 588 744 587 Q 734 586 735 575 Z", "M 469 292 Q 685 338 695 346 Q 702 353 699 361 Q 692 371 666 379 Q 638 385 611 374 Q 556 356 501 340 L 420 322 Q 362 313 301 306 Q 267 300 291 285 Q 321 266 396 277 Q 397 277 399 277 L 469 292 Z", "M 413 198 Q 449 258 469 292 L 501 340 Q 502 344 503 350 Q 494 371 473 393 Q 461 409 448 409 Q 435 408 436 394 Q 437 372 427 336 Q 423 330 420 322 L 399 277 Q 315 109 155 -4 Q 148 -8 144 -12 Q 138 -21 148 -22 Q 175 -28 274 40 Q 286 52 300 62 Q 355 113 406 186 L 413 198 Z", "M 593 89 Q 615 122 628 159 Q 643 202 655 213 Q 665 225 657 235 Q 647 250 621 260 Q 602 270 583 268 Q 570 265 576 250 Q 592 211 559 130 Q 556 124 553 117 L 531 86 Q 476 25 336 -4 Q 320 -11 339 -15 Q 486 -37 566 53 L 593 89 Z", "M 566 53 Q 680 -55 715 -55 Q 775 -55 864 -34 Q 897 -27 893 -18 Q 892 -14 859 -6 Q 670 36 599 85 Q 596 88 593 89 L 553 117 Q 520 142 488 170 Q 451 203 421 200 Q 417 200 413 198 C 393 195 393 195 406 186 Q 484 132 531 86 L 566 53 Z", "M 727 392 Q 758 364 794 332 Q 806 320 819 321 Q 829 321 833 333 Q 837 346 830 371 Q 821 405 723 434 Q 710 437 704 435 Q 700 431 701 420 Q 704 410 727 392 Z"], "medians": [[[327, 785], [353, 780], [475, 809], [505, 810]], [[263, 806], [293, 779], [303, 760], [321, 571], [320, 563], [309, 556]], [[333, 709], [366, 704], [458, 724], [492, 724]], [[337, 633], [380, 630], [447, 646], [492, 648]], [[102, 507], [157, 499], [420, 550], [492, 559], [522, 554]], [[349, 494], [323, 487], [309, 448], [287, 414], [282, 385], [311, 389], [446, 441], [452, 435]], [[440, 493], [487, 447], [496, 427]], [[772, 805], [729, 800], [652, 731], [568, 684]], [[791, 673], [736, 672], [660, 604], [594, 564], [584, 567], [581, 558]], [[813, 549], [762, 548], [731, 519], [676, 483], [610, 450], [573, 439]], [[293, 297], [321, 291], [383, 295], [506, 318], [636, 355], [688, 356]], [[448, 397], [465, 348], [415, 250], [355, 156], [253, 48], [188, 2], [152, -15]], [[585, 257], [617, 222], [584, 121], [552, 73], [523, 45], [476, 17], [420, -1], [347, -7]], [[417, 187], [427, 188], [458, 172], [610, 46], [714, -12], [771, -19], [888, -20]], [[709, 426], [789, 376], [819, 336]]], "radStrokes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}