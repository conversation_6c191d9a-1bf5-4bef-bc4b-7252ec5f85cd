{"strokes": ["M 297 690 Q 363 780 363 783 Q 362 786 362 788 Q 359 798 335 820 Q 313 836 296 838 Q 280 837 286 817 Q 310 760 143 563 Q 133 554 131 549 Q 128 539 141 541 Q 180 545 279 667 L 297 690 Z", "M 339 527 Q 357 549 391 595 Q 427 646 463 675 Q 479 685 467 697 Q 454 707 420 726 Q 411 729 328 699 Q 325 699 297 690 C 268 681 249 672 279 667 Q 295 661 364 676 Q 371 677 373 675 Q 377 672 373 661 Q 340 576 319 536 Q 315 529 312 521 C 300 493 320 504 339 527 Z", "M 178 490 Q 168 494 150 497 Q 138 500 135 495 Q 128 489 136 474 Q 166 405 181 295 Q 182 262 201 239 Q 217 218 223 233 Q 226 243 227 256 L 227 278 Q 226 291 224 305 Q 203 432 201 462 C 199 483 199 483 178 490 Z", "M 384 286 Q 399 265 413 258 Q 423 251 435 270 Q 450 297 475 437 Q 482 464 502 487 Q 512 497 502 510 Q 489 523 450 546 Q 438 552 365 533 Q 361 533 339 527 L 312 521 Q 285 517 258 509 Q 212 497 178 490 C 149 484 173 452 201 462 Q 200 463 202 463 Q 233 473 280 484 L 309 490 Q 342 500 384 507 Q 409 511 419 503 Q 432 488 430 472 Q 409 330 392 312 C 380 290 380 290 384 286 Z", "M 335 387 Q 357 393 377 395 Q 396 399 389 410 Q 379 422 359 424 Q 349 425 337 422 L 292 410 Q 261 400 233 391 Q 220 387 237 375 Q 243 372 293 380 L 335 387 Z", "M 330 309 Q 331 352 335 387 L 337 422 Q 337 426 338 430 Q 338 467 337 468 Q 322 483 309 490 C 283 505 270 512 280 484 Q 289 460 292 410 L 293 380 Q 293 349 294 298 C 295 268 329 279 330 309 Z", "M 227 256 Q 233 256 241 257 Q 320 275 384 286 C 414 291 418 297 392 312 Q 389 315 381 316 Q 362 317 330 309 L 294 298 Q 258 288 227 278 C 198 269 197 255 227 256 Z", "M 120 122 Q 107 121 105 111 Q 102 98 111 90 Q 133 74 165 53 Q 175 49 187 58 Q 217 80 408 156 Q 433 166 452 180 Q 465 187 466 196 Q 462 202 449 199 Q 173 121 120 122 Z", "M 660 610 Q 720 635 917 636 Q 936 636 941 645 Q 945 655 929 667 Q 878 703 814 681 Q 762 669 687 653 Q 681 652 676 650 L 617 639 Q 554 629 490 616 Q 471 613 486 599 Q 499 587 516 583 Q 535 579 549 584 Q 574 593 603 599 L 660 610 Z", "M 593 471 Q 626 531 660 610 L 676 650 Q 709 743 726 767 Q 733 780 726 793 Q 719 805 686 822 Q 655 835 637 831 Q 618 825 631 805 Q 653 774 645 743 Q 633 691 617 639 L 603 599 Q 564 487 477 332 Q 470 320 468 313 Q 467 301 480 307 Q 507 319 570 427 L 593 471 Z", "M 609 245 Q 612 294 615 329 L 617 357 Q 620 409 623 429 Q 624 435 623 440 C 623 456 623 456 607 464 Q 600 468 593 471 C 566 484 563 456 570 427 Q 600 304 548 142 Q 515 82 548 41 Q 555 28 564 33 Q 595 54 608 220 L 609 245 Z", "M 623 440 Q 633 441 651 448 Q 742 472 758 466 Q 770 460 768 434 Q 786 244 776 95 Q 775 65 757 59 Q 747 56 694 69 Q 681 73 682 65 Q 683 56 698 46 Q 732 15 759 -17 Q 772 -33 789 -33 Q 807 -29 828 23 Q 855 75 849 129 Q 839 256 827 374 Q 823 420 842 458 Q 852 474 838 487 Q 820 503 781 515 Q 760 525 743 512 Q 730 505 703 494 Q 636 472 607 464 C 578 456 594 432 623 440 Z", "M 615 329 Q 616 329 620 329 Q 675 339 716 344 Q 738 348 729 361 Q 719 374 694 377 Q 655 380 617 357 C 591 342 585 329 615 329 Z", "M 608 220 Q 611 220 617 220 Q 678 227 723 233 Q 745 237 737 249 Q 727 262 703 267 Q 658 270 609 245 C 582 232 578 220 608 220 Z"], "medians": [[[296, 825], [315, 804], [322, 783], [299, 735], [226, 634], [170, 572], [139, 548]], [[284, 673], [371, 695], [391, 694], [412, 684], [362, 584], [319, 523]], [[144, 487], [164, 470], [175, 448], [213, 239]], [[185, 488], [206, 480], [379, 524], [418, 526], [441, 520], [463, 495], [433, 347], [416, 297], [419, 273]], [[236, 383], [346, 407], [380, 405]], [[288, 481], [309, 463], [314, 440], [313, 335], [308, 316], [298, 306]], [[234, 262], [239, 269], [384, 307]], [[119, 108], [176, 93], [391, 165], [460, 194]], [[488, 608], [539, 604], [698, 639], [829, 660], [877, 662], [931, 649]], [[640, 817], [669, 798], [685, 774], [662, 692], [616, 566], [568, 463], [528, 388], [476, 315]], [[597, 462], [600, 368], [589, 209], [562, 97], [559, 43]], [[613, 459], [629, 456], [660, 462], [761, 492], [782, 484], [803, 465], [798, 394], [812, 197], [814, 107], [809, 70], [781, 23], [689, 63]], [[618, 336], [632, 348], [660, 356], [695, 359], [720, 354]], [[613, 226], [661, 245], [706, 249], [727, 243]]], "radStrokes": [0, 1, 2, 3, 4, 5, 6, 7]}